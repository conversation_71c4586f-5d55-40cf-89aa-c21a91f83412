<!-- SWAAGAT Landing Page Component -->
<div class="page-layout">
  <!-- Header Section -->
  <header class="header">
    <div class="container">
      <div class="header-content">
        <div class="logo">
          <div class="logo-placeholder">
            <mat-icon class="logo-icon">account_balance</mat-icon>
          </div>
          <div class="logo-text">
            <h1>SWAAGAT</h1>
            <p>Government of Tripura</p>
          </div>
        </div>
        <nav class="nav" [class.active]="mobileMenuOpen">
          <a href="#about" class="nav-link" (click)="toggleMobileMenu()">About</a>
          <a href="#advantages" class="nav-link" (click)="toggleMobileMenu()">Advantages</a>
          <a href="#services" class="nav-link" (click)="toggleMobileMenu()">Services</a>
          <a href="#journey" class="nav-link" (click)="toggleMobileMenu()">Journey</a>
          <a href="#dashboard" class="nav-link" (click)="toggleMobileMenu()">Dashboard</a>
          <button mat-raised-button color="primary" routerLink="/auth/registration" class="cta-button">Get Started</button>
        </nav>
        <button class="mobile-menu-toggle" (click)="toggleMobileMenu()">
          <mat-icon>{{ mobileMenuOpen ? 'close' : 'menu' }}</mat-icon>
        </button>
      </div>
    </div>
  </header>

  <!-- Carousel Section -->
  <section class="carousel-section">
    <div class="carousel-container">
      <div class="carousel-slides" [style.transform]="'translateX(-' + (currentSlide * 100) + '%)'">
        <div class="carousel-slide" *ngFor="let slide of carouselSlides">
          <div class="slide-content">
            <div class="slide-text">
              <h2 class="slide-title">{{ slide.title }}</h2>
              <h3 class="slide-subtitle">{{ slide.subtitle }}</h3>
              <p class="slide-description">{{ slide.description }}</p>
              <button mat-raised-button color="accent" [routerLink]="slide.buttonLink" class="slide-button">
                {{ slide.buttonText }}
              </button>
            </div>
            <div class="slide-image">
              <img [src]="slide.image" [alt]="slide.title" class="slide-img">
            </div>
          </div>
        </div>
      </div>
      <!-- Carousel Controls -->
      <div class="carousel-controls">
        <button mat-icon-button (click)="prevSlide()" class="carousel-btn prev">
          <mat-icon>chevron_left</mat-icon>
        </button>
        <button mat-icon-button (click)="nextSlide()" class="carousel-btn next">
          <mat-icon>chevron_right</mat-icon>
        </button>
      </div>
      <!-- Carousel Indicators -->
      <div class="carousel-indicators">
        <button *ngFor="let slide of carouselSlides; let i = index" (click)="goToSlide(i)"
                [class.active]="i === currentSlide" class="indicator"></button>
      </div>
    </div>
  </section>

  <!-- About Swaagat Section -->
  <section id="about" class="about-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">About SWAAGAT</h2>
        <p class="section-subtitle">Single Window Agency for Approvals, Grievances & Applications in Tripura</p>
      </div>
      <div class="about-content">
        <div class="about-text">
          <p>SWAAGAT is Tripura's comprehensive digital platform designed to streamline business processes and facilitate investments. Our mission is to provide a seamless, transparent, and efficient experience for entrepreneurs and investors looking to establish their presence in Tripura.</p>
        </div>
        <div class="features-grid">
          <mat-card class="feature-card" *ngFor="let feature of features">
            <mat-card-content>
              <div class="feature-icon">
                <i [class]="'feather-' + feature.icon"></i>
              </div>
              <h3 class="feature-title">{{ feature.title }}</h3>
              <p class="feature-description">{{ feature.description }}</p>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </div>
  </section>

  <!-- Advantages Section -->
  <section id="advantages" class="advantages-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Advantages of Investing in Tripura</h2>
        <p class="section-subtitle">Discover why Tripura is the ideal destination for your business</p>
      </div>
      <div class="advantages-grid">
        <mat-card class="advantage-card" *ngFor="let advantage of advantages">
          <mat-card-content>
            <div class="advantage-icon">
              <i [class]="'feather-' + advantage.icon"></i>
            </div>
            <h3 class="advantage-title">{{ advantage.title }}</h3>
            <p class="advantage-description">{{ advantage.description }}</p>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </section>

  <!-- Services Section -->
  <section id="services" class="services-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Services in SWAAGAT</h2>
        <p class="section-subtitle">Comprehensive services to support your business journey</p>
      </div>
      <div class="services-accordion">
        <mat-expansion-panel *ngFor="let service of services" class="service-panel">
          <mat-expansion-panel-header>
            <mat-panel-title class="service-title">
              <i [class]="'feather-' + service.icon" class="service-icon"></i>
              {{ service.title }}
            </mat-panel-title>
            <mat-panel-description class="service-description">{{ service.description }}</mat-panel-description>
          </mat-expansion-panel-header>
          <div class="service-content">
            <ul class="service-features">
              <li *ngFor="let feature of service.features" class="service-feature">
                <mat-icon class="feature-check">check_circle</mat-icon>
                {{ feature }}
              </li>
            </ul>
          </div>
        </mat-expansion-panel>
      </div>
    </div>
  </section>

  <!-- Investment Journey Section -->
  <section id="journey" class="journey-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Investment Journey</h2>
        <p class="section-subtitle">Simple steps to start your business in Tripura</p>
      </div>
      <div class="stepper-container">
        <mat-stepper [orientation]="isMobile ? 'vertical' : 'horizontal'" class="investment-stepper">
          <mat-step *ngFor="let step of investmentSteps; let i = index" [label]="step.label">
            <div class="step-content">
              <div class="step-number">{{ i + 1 }}</div>
              <h3 class="step-title">{{ step.label }}</h3>
              <p class="step-description">{{ step.description }}</p>
            </div>
          </mat-step>
        </mat-stepper>
      </div>
    </div>
  </section>

  <!-- Analytics Dashboard Section -->
  <section id="dashboard" class="dashboard-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Analytics Dashboard</h2>
        <p class="section-subtitle">Track your applications and monitor progress in real-time</p>
      </div>
      <div class="dashboard-content">
        <div class="dashboard-text">
          <h3>Comprehensive Analytics</h3>
          <p>Get detailed insights into your application status, processing times, and business metrics through our advanced analytics dashboard.</p>
          <ul class="dashboard-features">
            <li>Real-time application tracking</li>
            <li>Performance metrics and KPIs</li>
            <li>Document management system</li>
            <li>Automated notifications and alerts</li>
          </ul>
          <button mat-raised-button color="primary" routerLink="/dashboard/home" class="dashboard-cta">Access Dashboard</button>
        </div>
        <div class="dashboard-preview">
          <img src="assets/dashboard-mockup.png" alt="Analytics Dashboard Mockup" class="dashboard-img">
        </div>
      </div>
    </div>
  </section>
</div>