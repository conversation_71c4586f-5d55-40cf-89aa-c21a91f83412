/* SWAAGAT Landing Page Styles */
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap');
@import url('https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/dist/feather.min.css');

/* Header Section (60% Primary Color) */
.header {
  background-color: var(--color-primary);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
  }

  .logo {
    display: flex;
    align-items: center;
    gap: 12px;

    .logo-placeholder {
      width: 60px;
      height: 60px;
      background-color: var(--color-poppy-blue);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;

      .logo-icon {
        color: var(--color-text-white);
        font-size: 2.5rem;
      }
    }

    .logo-text {
      h1 {
        margin: 0;
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--color-text-dark);
      }

      p {
        margin: 0;
        font-size: 1rem;
        color: var(--color-text-medium);
      }
    }
  }

  .nav {
    display: flex;
    align-items: center;
    gap: 2.5rem;

    .nav-link {
      color: var(--color-text-dark);
      text-decoration: none;
      font-weight: 600;
      position: relative;
      transition: color 0.3s ease;

      &:after {
        content: '';
        position: absolute;
        width: 0;
        height: 2px;
        bottom: -4px;
        left: 0;
        background-color: var(--color-poppy-blue);
        transition: width 0.3s ease;
      }

      &:hover:after {
        width: 100%;
      }

      &:hover {
        color: var(--color-poppy-blue);
      }
    }

    .cta-button {
      background-color: var(--color-poppy-green) !important;
      color: var(--color-text-white) !important;
      padding: 12px 24px;
      font-weight: 600;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--color-text-dark);
    font-size: 1.5rem;
  }

  .nav.active {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 80px;
    left: 0;
    right: 0;
    background-color: var(--color-primary);
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

/* Carousel Section (30% Secondary Color) */
.carousel-section {
  background-color: var(--color-secondary);
  margin-top: 80px;
  position: relative;
  overflow: hidden;

  .carousel-container {
    position: relative;
    height: 600px;
  }

  .carousel-slides {
    display: flex;
    transition: transform 0.5s ease-in-out;
    height: 100%;
  }

  .carousel-slide {
    min-width: 100%;
    display: flex;
    align-items: center;

    .slide-content {
      display: flex;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
      gap: 4rem;
    }

    .slide-text {
      flex: 1;

      .slide-title {
        font-size: 3.5rem;
        font-weight: 700;
        color: var(--color-text-dark);
        margin-bottom: 1rem;
      }

      .slide-subtitle {
        font-size: 1.8rem;
        color: var(--color-text-medium);
        margin-bottom: 1rem;
      }

      .slide-description {
        font-size: 1.2rem;
        color: var(--color-text-medium);
        margin-bottom: 2rem;
        line-height: 1.6;
      }

      .slide-button {
        background-color: var(--color-poppy-yellow) !important;
        color: var(--color-text-dark) !important;
        padding: 12px 24px;
        font-size: 1rem;
        font-weight: 600;
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.05);
        }
      }
    }

    .slide-image {
      flex: 1;

      .slide-img {
        width: 100%;
        height: 500px;
        object-fit: cover;
        border-radius: 12px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .carousel-controls {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;

    .carousel-btn {
      background-color: rgba(255, 255, 255, 0.9);
      color: var(--color-text-dark);
      border-radius: 50%;
      transition: background-color 0.3s ease;

      &:hover {
        background-color: var(--color-poppy-yellow);
      }
    }
  }

  .carousel-indicators {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;

    .indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      border: none;
      background-color: rgba(255, 255, 255, 0.5);
      cursor: pointer;
      transition: background-color 0.3s ease;

      &.active {
        background-color: var(--color-poppy-yellow);
      }
    }
  }
}

/* About Section (60% Primary Color) */
.about-section {
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary-shade));
  padding: 100px 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .section-header {
    text-align: center;
    margin-bottom: 4rem;

    .section-title {
      font-size: 2.8rem;
      font-weight: 700;
      color: var(--color-text-dark);
      margin-bottom: 1rem;
    }

    .section-subtitle {
      font-size: 1.3rem;
      color: var(--color-text-medium);
      max-width: 600px;
      margin: 0 auto;
    }
  }

  .about-text {
    text-align: center;
    margin-bottom: 3rem;

    p {
      font-size: 1.2rem;
      color: var(--color-text-medium);
      line-height: 1.6;
      max-width: 800px;
      margin: 0 auto;
    }
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;

    .feature-card {
      background-color: var(--color-secondary);
      text-align: center;
      padding: 2rem;
      border-radius: 12px;
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      &:hover {
        transform: translateY(-10px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
      }

      .feature-icon {
        font-size: 3.5rem;
        color: var(--color-poppy-blue);
        margin-bottom: 1rem;
      }

      .feature-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--color-text-dark);
        margin-bottom: 1rem;
      }

      .feature-description {
        color: var(--color-text-medium);
        line-height: 1.6;
      }
    }
  }
}

/* Advantages Section (30% Tertiary Shade) */
.advantages-section {
  background-color: var(--color-tertiary-shade);
  padding: 100px 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .section-header {
    text-align: center;
    margin-bottom: 4rem;

    .section-title {
      font-size: 2.8rem;
      font-weight: 700;
      color: var(--color-text-dark);
      margin-bottom: 1rem;
    }

    .section-subtitle {
      font-size: 1.3rem;
      color: var(--color-text-medium);
      max-width: 600px;
      margin: 0 auto;
    }
  }

  .advantages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;

    .advantage-card {
      background-color: var(--color-primary);
      text-align: center;
      padding: 2rem;
      border-radius: 12px;
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      &:hover {
        transform: translateY(-10px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
      }

      .advantage-icon {
        font-size: 3.5rem;
        color: var(--color-poppy-red);
        margin-bottom: 1rem;
      }

      .advantage-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--color-text-dark);
        margin-bottom: 1rem;
      }

      .advantage-description {
        color: var(--color-text-medium);
        line-height: 1.6;
      }
    }
  }
}

/* Services Section (60% Primary Color) */
.services-section {
  background-color: var(--color-primary);
  padding: 100px 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .section-header {
    text-align: center;
    margin-bottom: 4rem;

    .section-title {
      font-size: 2.8rem;
      font-weight: 700;
      color: var(--color-text-dark);
      margin-bottom: 1rem;
    }

    .section-subtitle {
      font-size: 1.3rem;
      color: var(--color-text-medium);
      max-width: 600px;
      margin: 0 auto;
    }
  }

  .services-accordion {
    max-width: 800px;
    margin: 0 auto;

    .service-panel {
      background-color: var(--color-secondary);
      margin-bottom: 1rem;
      border-radius: 12px;
      transition: box-shadow 0.3s ease;

      &:hover {
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
      }

      .service-title {
        display: flex;
        align-items: center;
        gap: 12px;
        font-weight: 600;
        color: var(--color-text-dark);

        .service-icon {
          font-size: 1.5rem;
          color: var(--color-poppy-green);
        }
      }

      .service-description {
        color: var(--color-text-medium);
      }

      .service-content {
        padding: 1rem 0;

        .service-features {
          list-style: none;
          padding: 0;
          margin: 0;

          .service-feature {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            color: var(--color-text-dark);

            .feature-check {
              color: var(--color-poppy-green);
              font-size: 1.25rem;
            }
          }
        }
      }
    }
  }
}

/* Investment Journey Section (30% Secondary Shade) */
.journey-section {
  background-color: var(--color-secondary-shade);
  padding: 100px 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .section-header {
    text-align: center;
    margin-bottom: 4rem;

    .section-title {
      font-size: 2.8rem;
      font-weight: 700;
      color: var(--color-text-dark);
      margin-bottom: 1rem;
    }

    .section-subtitle {
      font-size: 1.3rem;
      color: var(--color-text-medium);
      max-width: 600px;
      margin: 0 auto;
    }
  }

  .stepper-container {
    max-width: 1000px;
    margin: 0 auto;

    .investment-stepper {
      background: transparent;

      .step-content {
        text-align: center;
        padding: 2rem;
        background-color: var(--color-primary);
        border-radius: 12px;
        margin-top: 1rem;

        .step-number {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          background-color: var(--color-poppy-blue);
          color: var(--color-text-white);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.5rem;
          font-weight: 700;
          margin: 0 auto 1rem;
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .step-title {
          font-size: 1.5rem;
          font-weight: 600;
          color: var(--color-text-dark);
          margin-bottom: 0.5rem;
        }

        .step-description {
          color: var(--color-text-medium);
          line-height: 1.6;
        }
      }
    }
  }
}

/* Analytics Dashboard Section (60% Primary Color) */
.dashboard-section {
  background-color: var(--color-primary);
  padding: 100px 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .section-header {
    text-align: center;
    margin-bottom: 4rem;

    .section-title {
      font-size: 2.8rem;
      font-weight: 700;
      color: var(--color-text-dark);
      margin-bottom: 1rem;
    }

    .section-subtitle {
      font-size: 1.3rem;
      color: var(--color-text-medium);
      max-width: 600px;
      margin: 0 auto;
    }
  }

  .dashboard-content {
    display: flex;
    align-items: center;
    gap: 4rem;

    .dashboard-text {
      flex: 1;

      h3 {
        font-size: 2.2rem;
        font-weight: 700;
        color: var(--color-text-dark);
        margin-bottom: 1rem;
      }

      p {
        font-size: 1.2rem;
        color: var(--color-text-medium);
        line-height: 1.6;
        margin-bottom: 2rem;
      }

      .dashboard-features {
        list-style: none;
        padding: 0;
        margin: 0 0 2rem 0;

        li {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;
          color: var(--color-text-dark);

          &::before {
            content: '✓';
            color: var(--color-poppy-green);
            font-weight: bold;
            font-size: 1.25rem;
          }
        }
      }

      .dashboard-cta {
        background-color: var(--color-poppy-orange) !important;
        color: var(--color-text-white) !important;
        padding: 12px 24px;
        font-size: 1rem;
        font-weight: 600;
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.05);
        }
      }
    }

    .dashboard-preview {
      flex: 1;

      .dashboard-img {
        width: 100%;
        max-height: 400px;
        object-fit: cover;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.02);
        }
      }
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .header {
    .nav {
      display: none;
    }

    .mobile-menu-toggle {
      display: block;
    }
  }

  .carousel-section {
    .carousel-container {
      height: 500px;
    }

    .carousel-slide {
      .slide-content {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
      }

      .slide-text {
        .slide-title {
          font-size: 2.5rem;
        }

        .slide-subtitle {
          font-size: 1.5rem;
        }

        .slide-description {
          font-size: 1rem;
        }
      }

      .slide-image {
        .slide-img {
          height: 300px;
        }
      }
    }
  }

  .about-section,
  .advantages-section,
  .services-section,
  .journey-section,
  .dashboard-section {
    padding: 60px 0;
  }

  .about-section .features-grid,
  .advantages-section .advantages-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-content {
    flex-direction: column;
    gap: 2rem;
  }
}

@media (max-width: 480px) {
  .header {
    .header-content {
      padding: 0.75rem 0;
    }

    .logo {
      .logo-text {
        h1 {
          font-size: 1.5rem;
        }

        p {
          font-size: 0.85rem;
        }
      }
    }
  }

  .carousel-section {
    .carousel-container {
      height: 400px;
    }

    .carousel-slide {
      .slide-text {
        .slide-title {
          font-size: 2rem;
        }

        .slide-subtitle {
          font-size: 1.2rem;
        }

        .slide-description {
          font-size: 0.9rem;
        }
      }

      .slide-image {
        .slide-img {
          height: 200px;
        }
      }
    }
  }

  .section-header {
    .section-title {
      font-size: 2.2rem;
    }

    .section-subtitle {
      font-size: 1.1rem;
    }
  }
}