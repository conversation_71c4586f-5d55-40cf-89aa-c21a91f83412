import { Component, OnInit, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatStepperModule } from '@angular/material/stepper';
import { RouterLink } from '@angular/router';

interface CarouselSlide {
  title: string;
  subtitle: string;
  description: string;
  buttonText: string;
  buttonLink: string;
  image: string;
}

interface Feature {
  icon: string;
  title: string;
  description: string;
}

interface Advantage {
  icon: string;
  title: string;
  description: string;
}

interface Service {
  title: string;
  description: string;
  features: string[];
  icon: string;
}

interface InvestmentStep {
  label: string;
  description: string;
}

@Component({
  selector: 'app-swaagat-landing-page',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatExpansionModule,
    MatStepperModule,
    RouterLink
  ],
  templateUrl: './swaagat-landing-page.component.html',
  styleUrls: ['./swaagat-landing-page.component.scss']
})
export class SwaagatLandingPageComponent implements OnInit {
  currentSlide = 0;
  mobileMenuOpen = false;
  isMobile = window.innerWidth <= 768;

  carouselSlides: CarouselSlide[] = [
    {
      title: 'Welcome to SWAAGAT',
      subtitle: 'Your Gateway to Business in Tripura',
      description: 'Streamline your business setup with our comprehensive digital platform for approvals and grievances.',
      buttonText: 'Get Started',
      buttonLink: '/auth/registration',
      image: 'assets/slide1.jpg'
    },
    {
      title: 'Fast-Track Approvals',
      subtitle: 'Efficient and Transparent',
      description: 'Experience hassle-free processing with time-bound approvals and real-time tracking.',
      buttonText: 'Learn More',
      buttonLink: '/dashboard/home',
      image: 'assets/slide2.jpg'
    },
    {
      title: 'Investment Opportunities',
      subtitle: 'Explore Tripura’s Potential',
      description: 'Discover lucrative opportunities in India’s gateway to Southeast Asia.',
      buttonText: 'Explore Now',
      buttonLink: '/dashboard/home',
      image: 'assets/slide3.jpg'
    },
    {
      title: 'Digital Transformation',
      subtitle: 'Paperless Processing',
      description: 'Leverage our digital-first approach for seamless business operations.',
      buttonText: 'Discover Now',
      buttonLink: '/dashboard/home',
      image: 'assets/slide4.jpg'
    },
    {
      title: 'Support for Growth',
      subtitle: 'Investor-Friendly Policies',
      description: 'Benefit from comprehensive support and incentives for your business in Tripura.',
      buttonText: 'Start Now',
      buttonLink: '/auth/registration',
      image: 'assets/slide5.jpg'
    }
  ];

  features: Feature[] = [
    {
      icon: 'clock',
      title: 'Quick Processing',
      description: 'Fast-track your applications with automated workflows and real-time tracking.'
    },
    {
      icon: 'shield',
      title: 'Secure Platform',
      description: 'Enterprise-grade security ensuring your data and documents are protected.'
    },
    {
      icon: 'users',
      title: 'Expert Support',
      description: '24/7 dedicated support team to guide you through every step.'
    },
    {
      icon: 'globe',
      title: 'Digital Access',
      description: 'Access services anytime, anywhere with our responsive web platform.'
    }
  ];

  advantages: Advantage[] = [
    {
      icon: 'map-pin',
      title: 'Strategic Location',
      description: 'Gateway to Southeast Asia with excellent connectivity to Bangladesh and Myanmar.'
    },
    {
      icon: 'trending-up',
      title: 'Growing Economy',
      description: 'Rapidly developing economy with government support for new investments.'
    },
    {
      icon: 'dollar-sign',
      title: 'Cost Effective',
      description: 'Lower operational costs and competitive land prices for business setup.'
    },
    {
      icon: 'award',
      title: 'Government Support',
      description: 'Comprehensive policy support and incentives for investors.'
    }
  ];

  services: Service[] = [
    {
      title: 'Business Registration',
      description: 'Complete business registration and licensing services.',
      icon: 'briefcase',
      features: [
        'Company Registration',
        'Trade License',
        'GST Registration',
        'Professional Tax Registration'
      ]
    },
    {
      title: 'Industrial Approvals',
      description: 'All industrial clearances and approvals under one roof.',
      icon: 'factory',
      features: [
        'Environmental Clearance',
        'Fire Safety Certificate',
        'Pollution Control Board NOC',
        'Factory License'
      ]
    },
    {
      title: 'Investment Facilitation',
      description: 'End-to-end investment facilitation and support services.',
      icon: 'dollar-sign',
      features: [
        'Land Acquisition Support',
        'Investment Incentives',
        'Project Monitoring',
        'Grievance Resolution'
      ]
    }
  ];

  investmentSteps: InvestmentStep[] = [
    {
      label: 'Registration',
      description: 'Create your account and complete profile verification.'
    },
    {
      label: 'Application',
      description: 'Submit your investment proposal with required documents.'
    },
    {
      label: 'Review',
      description: 'Our experts review and process your application.'
    },
    {
      label: 'Approval',
      description: 'Receive approvals and start your business journey.'
    }
  ];

  @HostListener('window:resize', ['$event'])
  onResize(event: Event) {
    this.isMobile = window.innerWidth <= 768;
  }

  ngOnInit(): void {
    this.startCarousel();
  }

  startCarousel(): void {
    setInterval(() => {
      this.nextSlide();
    }, 5000);
  }

  nextSlide(): void {
    this.currentSlide = (this.currentSlide + 1) % this.carouselSlides.length;
  }

  prevSlide(): void {
    this.currentSlide = this.currentSlide === 0 ? this.carouselSlides.length - 1 : this.currentSlide - 1;
  }

  goToSlide(index: number): void {
    this.currentSlide = index;
  }

  toggleMobileMenu(): void {
    this.mobileMenuOpen = !this.mobileMenuOpen;
  }
}