/* SWAAGAT Landing Page Styles */
@import url('https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700;800&display=swap');
@import url('https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/dist/feather.min.css');

/* Global Styles */
.page-layout {
  font-family: 'Manrope', sans-serif;
}

/* Header Section (60% Primary Color) */
.header {
  background-color: var(--color-primary);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  .container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 16px;
  }

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0;
  }

  .logo {
    display: flex;
    align-items: center;
    gap: 12px;

    .logo-img {
      width: 60px;
      height: 60px;
      object-fit: contain;
    }

    .logo-text {
      h1 {
        margin: 0;
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--color-text-dark);
      }

      p {
        margin: 0;
        font-size: 1rem;
        color: var(--color-text-medium);
      }
    }
  }

  .nav {
    display: flex;
    align-items: center;
    gap: 2rem;

    .nav-link {
      color: var(--color-text-dark);
      text-decoration: none;
      font-weight: 600;
      font-size: 1rem;
      position: relative;
      transition: color 0.3s ease;

      &:after {
        content: '';
        position: absolute;
        width: 0;
        height: 2px;
        bottom: -4px;
        left: 0;
        background-color: var(--color-poppy-blue);
        transition: width 0.3s ease;
      }

      &:hover:after {
        width: 100%;
      }

      &:hover {
        color: var(--color-poppy-blue);
      }
    }

    .cta-button {
      background-color: var(--color-poppy-blue) !important;
      color: var(--color-text-white) !important;
      padding: 10px 20px;
      font-weight: 600;
      border-radius: 8px;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--color-text-dark);
    font-size: 1.5rem;
  }

  .nav.active {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 80px;
    left: 0;
    right: 0;
    background-color: var(--color-primary);
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

/* Hero Banner (Carousel) Section (30% Secondary Color) */
.banner-section {
  margin-top: 80px;
  position: relative;
  overflow: hidden;

  .banner-container {
    height: 500px;

    .banner-slides {
      display: flex;
      transition: transform 0.5s ease-in-out;
      height: 100%;
    }

    .banner-slide {
      min-width: 100%;
      position: relative;

      .banner-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .banner-overlay {
        position: absolute;
        inset: 0;
        background-color: rgba(0, 0, 0, 0.5);
      }

      .banner-content {
        position: absolute;
        inset: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        color: var(--color-text-white);
        padding: 16px;

        .banner-title {
          font-size: 3.5rem;
          font-weight: 800;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
          margin-bottom: 1rem;
        }

        .banner-subtitle {
          font-size: 1.5rem;
          font-weight: 500;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
          margin-bottom: 2rem;
        }

        .banner-button {
          background-color: var(--color-poppy-blue) !important;
          color: var(--color-text-white) !important;
          padding: 12px 24px;
          font-size: 1rem;
          font-weight: 600;
          border-radius: 8px;
          transition: transform 0.3s ease;

          &:hover {
            transform: scale(1.05);
          }
        }
      }
    }

    .carousel-controls {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 100%;
      display: flex;
      justify-content: space-between;
      padding: 0 16px;

      .carousel-btn {
        background-color: rgba(255, 255, 255, 0.9);
        color: var(--color-text-dark);
        border-radius: 50%;
        transition: background-color 0.3s ease;

        &:hover {
          background-color: var(--color-poppy-blue);
          color: var(--color-text-white);
        }
      }
    }

    .carousel-indicators {
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 10px;

      .indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: none;
        background-color: rgba(255, 255, 255, 0.5);
        cursor: pointer;
        transition: background-color 0.3s ease;

        &.active {
          background-color: var(--color-poppy-blue);
        }
      }
    }
  }
}

/* About Section (60% Primary Color) */
.about-section {
  background-color: var(--color-primary);
  padding: 64px 0;

  .container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 16px;
  }

  .section-header {
    text-align: center;
    margin-bottom: 3rem;

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--color-text-dark);
      margin-bottom: 1rem;
    }

    .section-subtitle {
      font-size: 1.25rem;
      color: var(--color-text-medium);
      margin-bottom: 1rem;
    }

    .section-description {
      font-size: 1.125rem;
      color: var(--color-text-medium);
      max-width: 896px;
      margin: 0 auto;
      line-height: 1.6;
    }
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;

    .feature-card {
      background-color: var(--color-secondary);
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      &:hover {
        transform: translateY(-8px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }

      .feature-icon {
        font-size: 2.5rem;
        color: var(--color-poppy-blue);
        margin-bottom: 1rem;
      }

      .feature-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--color-text-dark);
        margin-bottom: 0.5rem;
      }

      .feature-description {
        font-size: 1rem;
        color: var(--color-text-medium);
        line-height: 1.6;
      }
    }
  }

  .mission-statement {
    background-color: var(--color-poppy-blue);
    color: var(--color-text-white);
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    border-left: 4px solid var(--color-poppy-orange);

    .mission-title {
      font-size: 1.5rem;
      font-weight: 700;
      margin-bottom: 1rem;
    }

    .mission-text {
      font-size: 1.25rem;
      line-height: 1.6;
      margin-bottom: 1rem;
    }
  }
}

/* Advantages Section (30% Secondary Color) */
.advantages-section {
  background-color: var(--color-secondary);
  padding: 64px 0;

  .container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 16px;
  }

  .section-header {
    text-align: center;
    margin-bottom: 3rem;

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--color-text-dark);
    }

    .section-subtitle {
      font-size: 1.25rem;
      color: var(--color-text-medium);
    }
  }

  .advantages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;

    .advantage-card {
      background-color: var(--color-primary);
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      &:hover {
        transform: translateY(-8px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }

      .advantage-icon {
        font-size: 2.5rem;
        color: var(--color-poppy-orange);
        margin-bottom: 1rem;
      }

      .advantage-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--color-text-dark);
        margin-bottom: 0.5rem;
      }

      .advantage-description {
        font-size: 1rem;
        color: var(--color-text-medium);
        line-height: 1.6;
      }
    }
  }
}

/* Services Section (60% Primary Color) */
.services-section {
  background-color: var(--color-primary);
  padding: 64px 0;

  .container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 16px;
  }

  .section-header {
    text-align: center;
    margin-bottom: 3rem;

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--color-text-dark);
    }

    .section-subtitle {
      font-size: 1.25rem;
      color: var(--color-text-medium);
    }
  }

  .services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;

    .service-card {
      background-color: var(--color-secondary);
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      &:hover {
        transform: translateY(-8px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }

      .service-icon {
        font-size: 2.5rem;
        color: var(--color-poppy-green);
        margin-bottom: 1rem;
      }

      .service-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--color-text-dark);
        margin-bottom: 0.5rem;
      }

      .service-description {
        font-size: 1rem;
        color: var(--color-text-medium);
        margin-bottom: 1rem;
      }

      .service-features {
        list-style: none;
        padding: 0;
        margin: 0 0 1rem 0;

        li {
          font-size: 1rem;
          color: var(--color-text-medium);
          margin-bottom: 0.5rem;
          position: relative;
          padding-left: 1.5rem;

          &:before {
            content: '✓';
            color: var(--color-poppy-green);
            position: absolute;
            left: 0;
            font-weight: bold;
          }
        }
      }

      .service-button {
        background-color: var(--color-poppy-green) !important;
        color: var(--color-text-white) !important;
        border-radius: 8px;
      }
    }
  }
}

/* Investment Journey Section (30% Secondary Color) */
.journey-section {
  background-color: var(--color-secondary);
  padding: 64px 0;

  .container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 16px;
  }

  .section-header {
    text-align: center;
    margin-bottom: 3rem;

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--color-text-dark);
    }

    .section-subtitle {
      font-size: 1.25rem;
      color: var(--color-text-medium);
    }
  }

  .stepper-container {
    max-width: 1024px;
    margin: 0 auto;

    .investment-stepper {
      background: transparent;

      .step-content {
        text-align: center;
        padding: 2rem;
        background-color: var(--color-primary);
        border-radius: 8px;
        margin-top: 1rem;

        .step-number {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          background-color: var(--color-poppy-blue);
          color: var(--color-text-white);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.25rem;
          font-weight: 700;
          margin: 0 auto 1rem;
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .step-title {
          font-size: 1.5rem;
          font-weight: 700;
          color: var(--color-text-dark);
          margin-bottom: 0.5rem;
        }

        .step-description {
          font-size: 1rem;
          color: var(--color-text-medium);
          line-height: 1.6;
          margin-bottom: 1rem;
        }

        .step-features {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            font-size: 1rem;
            color: var(--color-text-medium);
            margin-bottom: 0.5rem;
            position: relative;
            padding-left: 1.5rem;

            &:before {
              content: '✓';
              color: var(--color-poppy-green);
              position: absolute;
              left: 0;
              font-weight: bold;
            }
          }
        }
      }
    }
  }
}

/* Investment Sectors Section (60% Primary Color) */
.sectors-section {
  background-color: var(--color-primary);
  padding: 64px 0;

  .container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 16px;
  }

  .section-header {
    text-align: center;
    margin-bottom: 3rem;

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--color-text-dark);
    }

    .section-subtitle {
      font-size: 1.25rem;
      color: var(--color-text-medium);
    }
  }

  .sectors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;

    .sector-card {
      background-color: var(--color-secondary);
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      &:hover {
        transform: translateY(-8px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }

      .sector-image {
        .sector-img {
          width: 100%;
          height: 200px;
          object-fit: cover;
          border-top-left-radius: 8px;
          border-top-right-radius: 8px;
        }
      }

      .sector-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--color-text-dark);
        margin-bottom: 0.5rem;
      }

      .sector-description {
        font-size: 1rem;
        color: var(--color-text-medium);
        margin-bottom: 1rem;
      }

      .sector-stats {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
        font-size: 1rem;
        color: var(--color-text-dark);

        strong {
          color: var(--color-poppy-blue);
        }
      }

      .sector-opportunities {
        font-size: 1rem;
        color: var(--color-text-medium);
        margin-bottom: 1rem;

        ul {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            margin-bottom: 0.5rem;
            position: relative;
            padding-left: 1.5rem;

            &:before {
              content: '✓';
              color: var(--color-poppy-green);
              position: absolute;
              left: 0;
              font-weight: bold;
            }
          }
        }
      }

      .sector-button {
        background-color: var(--color-poppy-green) !important;
        color: var(--color-text-white) !important;
        border-radius: 8px;
      }
    }
  }

  .cta-section {
    text-align: center;
    margin-top: 3rem;

    .cta-title {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--color-text-dark);
      margin-bottom: 1rem;
    }

    .cta-description {
      font-size: 1rem;
      color: var(--color-text-medium);
      margin-bottom: 1rem;
    }

    .cta-button {
      background-color: var(--color-poppy-blue) !important;
      color: var(--color-text-white) !important;
      padding: 12px 24px;
      font-size: 1rem;
      font-weight: 600;
      border-radius: 8px;
    }
  }
}

/* Analytics Dashboard Section (60% Primary Color) */
.dashboard-section {
  background-color: var(--color-poppy-blue);
  padding: 64px 0;
  color: var(--color-text-white);

  .container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 16px;
  }

  .section-header {
    text-align: center;
    margin-bottom: 3rem;

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
    }

    .section-subtitle {
      font-size: 1.25rem;
      opacity: 0.9;
    }
  }

  .dashboard-content {
    display: flex;
    gap: 2rem;
    align-items: center;

    .dashboard-stats {
      flex: 1;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1.5rem;

      .stat-card {
        background-color: var(--color-primary);
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        text-align: center;

        .stat-value {
          font-size: 2.5rem;
          font-weight: 700;
          color: var(--color-poppy-blue);
          margin-bottom: 0.5rem;
        }

        .stat-label {
          font-size: 1rem;
          color: var(--color-text-dark);
        }

        .stat-change {
          font-size: 0.875rem;
          color: var(--color-poppy-green);
        }
      }
    }

    .dashboard-preview {
      flex: 1;

      .dashboard-img {
        width: 100%;
        max-height: 400px;
        object-fit: cover;
        border-radius: 8px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.02);
        }
      }
    }
  }
}

/* Investor Support Section (30% Secondary Color) */
.support-section {
  background-color: var(--color-secondary);
  padding: 64px 0;

  .container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 16px;
  }

  .section-header {
    text-align: center;
    margin-bottom: 3rem;

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--color-text-dark);
    }

    .section-subtitle {
      font-size: 1.25rem;
      color: var(--color-text-medium);
    }
  }

  .support-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;

    .support-card {
      background-color: var(--color-primary);
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      &:hover {
        transform: translateY(-8px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }

      .support-icon {
        font-size: 2.5rem;
        color: var(--color-poppy-orange);
        margin-bottom: 1rem;
      }

      .support-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--color-text-dark);
        margin-bottom: 0.5rem;
      }

      .support-description {
        font-size: 1rem;
        color: var(--color-text-medium);
        margin-bottom: 1rem;
      }

      .support-button {
        background-color: var(--color-poppy-orange) !important;
        color: var(--color-text-white) !important;
        border-radius: 8px;
      }
    }
  }

  .contact-info {
    text-align: center;

    .contact-title {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--color-text-dark);
      margin-bottom: 1rem;
    }

    .contact-details {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;

      .contact-item {
        font-size: 1rem;
        color: var(--color-text-medium);

        strong {
          color: var(--color-text-dark);
          font-weight: 600;
        }

        p {
          margin: 0;
        }
      }
    }
  }
}

/* Footer Section */
.footer {
  background-color: var(--color-poppy-blue);
  color: var(--color-text-white);
  padding: 64px 0 32px;

  .container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 16px;
  }

  .footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;

    .footer-brand {
      h3 {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
      }

      p {
        font-size: 1rem;
        opacity: 0.9;
      }
    }

    .footer-links {
      h4 {
        font-size: 1.25rem;
        font-weight: 700;
        margin-bottom: 1rem;
      }

      ul {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
          margin-bottom: 0.5rem;

          a {
            color: var(--color-text-white);
            text-decoration: none;
            font-size: 1rem;
            transition: color 0.3s ease;

            &:hover {
              color: var(--color-poppy-orange);
            }
          }
        }
      }
    }

    .footer-contact {
      h4 {
        font-size: 1.25rem;
        font-weight: 700;
        margin-bottom: 1rem;
      }

      p {
        font-size: 1rem;
        margin-bottom: 0.5rem;
        opacity: 0.9;
      }
    }
  }

  .footer-bottom {
    text-align: center;
    font-size: 0.875rem;
    opacity: 0.9;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .header {
    .nav {
      display: none;
    }

    .mobile-menu-toggle {
      display: block;
    }
  }

  .banner-section {
    .banner-container {
      height: 400px;

      .banner-slide {
        .banner-content {
          .banner-title {
            font-size: 2.5rem;
          }

          .banner-subtitle {
            font-size: 1.25rem;
          }
        }
      }
    }
  }

  .about-section,
  .advantages-section,
  .services-section,
  .journey-section,
  .sectors-section,
  .dashboard-section,
  .support-section {
    padding: 48px 0;

    .section-header {
      .section-title {
        font-size: 2rem;
      }

      .section-subtitle {
        font-size: 1.125rem;
      }
    }
  }

  .dashboard-content {
    flex-direction: column;
  }

  .footer-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .header {
    .logo {
      .logo-text {
        h1 {
          font-size: 1.5rem;
        }

        p {
          font-size: 0.875rem;
        }
      }
    }
  }

  .banner-section {
    .banner-container {
      height: 300px;

      .banner-slide {
        .banner-content {
          .banner-title {
            font-size: 2rem;
          }

          .banner-subtitle {
            font-size: 1rem;
          }
        }
      }
    }
  }

  .section-header {
    .section-title {
      font-size: 1.75rem;
    }

    .section-subtitle {
      font-size: 1rem;
    }
  }
}